import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import { MemoryRouter, Routes, Route } from "react-router-dom";
import Reader from "./Reader";
import * as api from "../api/client";
import { summaryTexts } from "../constants/ui";

// 使用全局的 __advanceAndFlush__ 函数
declare global {
  var __advanceAndFlush__: (ms: number) => Promise<void>;
}

describe("Reader 摘要更新调试", () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.clearAllMocks();
  });

  it("调试摘要更新功能", async () => {
    console.log("=== 开始摘要更新调试测试 ===");
    console.log("=== summaryTexts.updated:", summaryTexts.updated);
    
    const first = {
      id: "s1",
      title: "T",
      content: { paragraphs: [{ index: 0, text: "p0" }] },
      summary_latest: null,
    } as any;

    const second = {
      id: "s1",
      title: "T",
      content: { paragraphs: [{ index: 0, text: "p0" }] },
      summary_latest: { text: "summary-updated" },
    } as any;

    console.log("=== 设置 API mock ===");
    const spy = vi.spyOn(api, "getSessionById")
      .mockResolvedValueOnce(first)
      .mockResolvedValueOnce(second);

    console.log("=== 首次渲染 ===");
    const { rerender } = render(
      <MemoryRouter initialEntries={["/reader/s1"]}>
        <Routes>
          <Route path="/reader/:id" element={<Reader />} />
        </Routes>
      </MemoryRouter>
    );

    console.log("=== 等待首次加载完成 ===");
    await __advanceAndFlush__(500);
    
    // 验证首次加载完成
    await waitFor(() => {
      expect(screen.queryByText("p0")).toBeInTheDocument();
    }, { timeout: 1000 });
    
    console.log("=== 首次加载完成，当前 DOM ===");
    console.log(screen.debug());
    
    console.log("=== 开始第二次渲染 ===");

    // 重新渲染以触发第二次 API 调用
    rerender(
      <MemoryRouter initialEntries={["/reader/s1"]}>
        <Routes>
          <Route path="/reader/:id" element={<Reader />} />
        </Routes>
      </MemoryRouter>
    );

    console.log("=== 等待第二次加载完成 ===");
    await __advanceAndFlush__(500);

    console.log("=== 第二次加载完成，当前 DOM ===");
    console.log(screen.debug());

    console.log("=== 查找摘要更新提示 ===");
    const allTexts = screen.getAllByText(/.*/).map(el => el.textContent);
    console.log("=== 当前所有文本:", allTexts);
    
    const hint = screen.queryByText(new RegExp(summaryTexts.updated));
    console.log("=== 查找结果:", hint ? "找到" : "未找到");
    
    if (hint) {
      console.log("=== 找到提示元素:", hint.textContent);
      
      console.log("=== 推进 2 秒等待提示隐藏 ===");
      await __advanceAndFlush__(2200);

      console.log("=== 验证提示已隐藏 ===");
      const hintAfter = screen.queryByText(new RegExp(summaryTexts.updated));
      console.log("=== 隐藏检查结果:", hintAfter ? "仍存在" : "已隐藏");
    } else {
      console.log("=== 未找到提示，测试失败 ===");
    }

    console.log("=== API 调用次数:", spy.mock.calls.length);
    spy.mockRestore();
    console.log("=== 调试测试完成 ===");
  }, 60000);
});
