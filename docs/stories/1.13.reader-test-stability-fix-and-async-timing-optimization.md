# 1.13 Reader 测试稳定性修复与异步推进机制优化

## Status
Draft

## Story

**As a** 开发者和 QA 工程师，
**I want** Reader.test.tsx 的测试用例能够稳定通过，不再因异步时序问题导致超时失败，
**so that** CI/CD 流水线能够可靠运行，开发效率不被测试不稳定性阻塞，并且测试能够真实反映代码质量。

## Acceptance Criteria

1. **测试稳定性达标**: Reader.test.tsx 中的所有测试用例在连续 10 次运行中成功率达到 95% 以上，单次测试执行时间控制在 10 秒以内。
2. **异步推进机制优化**: `__advanceAndFlush__` 工具能够可靠地推进 Vitest fake timers 与 React 渲染周期的同步，确保 DOM 更新可被测试稳定观测。
3. **时钟控制统一**: schedule 适配层与 fake timers 完全协调，所有定时器回调都能被测试环境正确控制和推进。
4. **测试路由修复**: 测试中的路由配置与组件期望完全匹配，避免因路由参数不匹配导致的组件早期退出。
5. **微任务让步生效**: 所有关键异步回调末尾的微任务让步能够确保 React 状态更新被正确提交到 DOM。
6. **调试能力增强**: 测试失败时能够提供清晰的调试信息，包括当前 DOM 状态、异步推进状态、API 调用状态等。

## Tasks / Subtasks

- [ ] **P1: 核心异步推进机制修复** (AC: 1, 2, 3)
  - [ ] 分析当前 `__advanceAndFlush__` 工具的实现，识别与 React 渲染周期不同步的问题
  - [ ] 优化推进序列：确保 `vi.advanceTimersByTimeAsync` → `vi.runOnlyPendingTimersAsync` → 微任务清空 → React act 的正确顺序
  - [ ] 验证 schedule 适配层在测试环境下使用 fake timers 的兼容性
  - [ ] 测试推进工具的边界情况：0ms 推进、大时间跨度推进、连续推进等

- [ ] **P2: 测试用例路由与环境修复** (AC: 4)
  - [ ] 修复测试路由配置，确保 `/reader/:id` 参数正确传递给组件
  - [ ] 验证 `useParams<{ id: string }>()` 在测试环境下能够正确获取路由参数
  - [ ] 清理 setup.ts 中可能存在的重复定义或冲突配置
  - [ ] 确保所有必要的 DOM API mock（window.scrollTo、document.hasFocus 等）正确配置

- [ ] **P3: 微任务让步机制验证与优化** (AC: 5)
  - [ ] 验证现有的 `schedule.microtask(() => {})` 调用是否在正确的位置
  - [ ] 测试微任务让步在不同异步场景下的效果：setTimeout 回调、rAF 回调、Promise 链等
  - [ ] 确保微任务让步不影响业务逻辑的正确性
  - [ ] 添加必要的微任务让步到遗漏的关键异步点

- [ ] **P4: 测试调试能力增强** (AC: 6)
  - [ ] 为测试失败场景添加详细的调试日志输出
  - [ ] 实现测试状态快照功能：DOM 状态、组件状态、API 调用历史等
  - [ ] 优化测试错误信息的可读性，包含具体的失败原因和上下文
  - [ ] 创建测试调试工具函数，便于快速定位异步时序问题

- [ ] **P5: 回归测试与稳定性验证** (AC: 1)
  - [ ] 设计自动化稳定性测试脚本，连续运行测试用例验证成功率
  - [ ] 在不同环境下验证修复效果：本地开发环境、CI 环境等
  - [ ] 建立测试性能基线，确保修复不影响测试执行效率
  - [ ] 文档化测试最佳实践，避免类似问题再次出现

## Dev Notes

### 前一个故事的关键洞察
从 1.12 故事的 QA Results 中可以看到，Reader 组件已经实现了流式响应、动态摘要更新、长加载提示等功能，并且已经添加了基础的测试覆盖。但是测试稳定性问题仍然存在，说明异步推进机制需要进一步优化。[Source: docs/stories/1.12.frontend-conversation-stream-and-dynamic-summary-mvp.md#L185-L194]

### 技术栈与测试框架
- **测试框架**: Vitest + React Testing Library，已在项目中配置并使用 [Source: docs/architecture.md#L160-L167]
- **测试环境**: jsdom 环境，通过 vite.config.ts 配置 [Source: apps/frontend/vite.config.ts#L13-L18]
- **异步控制**: 使用 Vitest fake timers 控制时间推进，通过 `vi.useFakeTimers()` 启用 [Source: apps/frontend/src/test/setup.ts#L68-L108]

### 当前异步推进机制分析
- **全局推进工具**: `__advanceAndFlush__` 已在 setup.ts 中定义，包含时钟推进、微任务清空、React act 包装 [Source: apps/frontend/src/test/setup.ts#L88-L108]
- **schedule 适配层**: Reader.tsx 中已实现统一的调度适配层，支持测试环境下的时钟控制 [Source: apps/frontend/src/pages/Reader.tsx#L17-L24]
- **微任务让步**: 关键异步回调已添加 `schedule.microtask(() => {})` 调用 [Source: HANDOVER_READER_TEST_FIX.md#L11-L16]

### 已知问题与修复点
1. **测试路由问题**: 测试使用的路由路径与组件期望的参数格式可能不匹配 [Source: HANDOVER_READER_TEST_FIX.md#L24-L27]
2. **推进机制不完全有效**: 尽管已添加微任务让步，测试仍然超时，说明推进序列需要优化 [Source: HANDOVER_READER_TEST_FIX.md#L17-L20]
3. **环境兼容性**: window.scrollTo 等 DOM API 需要正确 mock [Source: apps/frontend/src/test/setup.ts#L46-L56]

### 文件位置与结构
- **测试文件**: `apps/frontend/src/pages/Reader.test.tsx` - 主要的测试文件
- **调试测试**: `apps/frontend/src/pages/Reader.debug.test.tsx` - 用于调试的测试文件
- **测试配置**: `apps/frontend/src/test/setup.ts` - 全局测试环境配置
- **组件实现**: `apps/frontend/src/pages/Reader.tsx` - 被测试的组件

### Testing

#### Test file location
- 测试文件位于 `apps/frontend/src/pages/Reader.test.tsx` 和相关调试文件
- 遵循与组件同路径的测试文件组织约定 [Source: docs/architecture.md#L160-L167]

#### Test standards
- 使用统一错误模型断言，包含 trace_id 验证
- 网络和 5xx 错误通过 mock 验证，遵循静默失败策略
- 所有异步操作必须通过 fake timers 和推进工具控制 [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md#L263-L267]

#### Frameworks and patterns
- **Vitest + React Testing Library**: 主要测试框架组合
- **Fake Timers**: 使用 `vi.useFakeTimers()` 控制时间推进
- **全局推进工具**: 使用 `__advanceAndFlush__` 统一推进异步操作
- **Act 包装**: 确保 React 状态更新被正确处理 [Source: apps/frontend/src/test/setup.ts#L93-L108]

#### Story-specific testing requirements
- **异步推进验证**: 测试 `__advanceAndFlush__` 工具在不同时间跨度下的推进效果
- **微任务让步测试**: 验证 `schedule.microtask()` 调用对 DOM 更新观测的影响
- **路由参数测试**: 确保测试路由配置与组件期望的参数格式匹配
- **稳定性测试**: 连续运行测试验证成功率，目标 95% 以上
- **性能测试**: 单次测试执行时间控制在 10 秒以内

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-07 | 1.0 | 初始故事创建，基于 Reader.test.tsx 测试稳定性问题分析 | Bob (SM) |

## Dev Agent Record

*This section will be populated by the development agent during implementation*

## QA Results

*This section will be populated by the QA agent during review*
